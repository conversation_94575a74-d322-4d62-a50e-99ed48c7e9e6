#!/usr/bin/env python
"""
Script para crear datos de ejemplo en el sistema de inventario
"""
import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from inventory.models import Product

def create_sample_products():
    """Crear productos de ejemplo"""
    sample_products = [
        {
            'name': 'Laptop Dell XPS 13',
            'price': 1299.99,
            'stock': 15
        },
        {
            'name': 'Mouse Logitech MX Master 3',
            'price': 99.99,
            'stock': 50
        },
        {
            'name': 'Teclado Mecánico Corsair K95',
            'price': 199.99,
            'stock': 25
        },
        {
            'name': 'Monitor Samsung 27" 4K',
            'price': 399.99,
            'stock': 8
        },
        {
            'name': 'Auriculares Sony WH-1000XM4',
            'price': 349.99,
            'stock': 30
        }
    ]
    
    print("Creando productos de ejemplo...")
    
    for product_data in sample_products:
        product, created = Product.objects.get_or_create(
            name=product_data['name'],
            defaults={
                'price': product_data['price'],
                'stock': product_data['stock']
            }
        )
        
        if created:
            print(f"✓ Creado: {product.name}")
        else:
            print(f"- Ya existe: {product.name}")
    
    print(f"\nTotal de productos en la base de datos: {Product.objects.count()}")

if __name__ == '__main__':
    create_sample_products()
